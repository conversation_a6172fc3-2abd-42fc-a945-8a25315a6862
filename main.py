#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信智能聊天机器人主程序
基于wxauto实现微信消息监听和智能回复功能
支持多种LLM模型：DeepSeek、ChatGPT、Qwen
"""

import sys
import os
import argparse
import time
from pathlib import Path

# 添加CustomFunction目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "CustomFunction"))

from CustomFunction.Config import ConfigManager
from CustomFunction.WeChatBot import WeChatBot
from CustomFunction.LLM import create_llm_manager
from CustomFunction.Agent import create_agent_from_config


def setup_config():
    """设置初始配置"""
    config = ConfigManager()
    
    print("=== 微信智能聊天机器人配置向导 ===")
    print("首次运行需要配置LLM模型和Agent")
    
    # 检查是否有可用的LLM配置
    enabled_models = config.get_enabled_llm_models()
    if not enabled_models:
        print("\n未检测到已配置的LLM模型，请先配置至少一个模型：")
        setup_llm_config(config)
    
    # 检查Agent配置
    agents = config.get_agent_config()
    if not agents or not any(agent.get("enabled", True) for agent in agents.values()):
        print("\n未检测到可用的Agent配置，使用默认配置")
        setup_default_agent(config)
    
    return config


def setup_llm_config(config: ConfigManager):
    """配置LLM模型"""
    models = ["deepseek", "chatgpt", "qwen"]
    
    for model in models:
        print(f"\n配置 {model.upper()} 模型:")
        api_key = input(f"请输入 {model.upper()} API Key (留空跳过): ").strip()
        
        if api_key:
            config.set_llm_api_key(model, api_key)
            print(f"{model.upper()} 配置成功")
            
            # 设置为当前模型（如果是第一个配置的）
            if not config.get_enabled_llm_models():
                config.set_current_llm(model)
        else:
            print(f"跳过 {model.upper()} 配置")


def setup_default_agent(config: ConfigManager):
    """设置默认Agent"""
    default_agent = {
        "name": "智能助手",
        "identity": "友善的AI助手",
        "llm_model": config.get_current_llm(),
        "prompt": "你是一个友善、有帮助的AI助手。请用简洁、自然的语言回复用户的消息，就像朋友间的聊天一样。",
        "enabled": True
    }
    
    config.add_agent_config("default", default_agent)
    config.set_current_agent("default")
    print("默认Agent配置完成")


def interactive_mode(bot: WeChatBot):
    """交互模式"""
    print("\n=== 进入交互模式 ===")
    print("可用命令:")
    print("  status - 查看机器人状态")
    print("  add <好友名> - 添加监听好友")
    print("  remove <好友名> - 移除监听好友")
    print("  send <好友名> <消息> - 发送消息")
    print("  toggle - 切换自动回复开关")
    print("  reload - 重新加载配置")
    print("  quit - 退出程序")
    print("  help - 显示帮助")
    
    while True:
        try:
            command = input("\n> ").strip().split()
            if not command:
                continue
            
            cmd = command[0].lower()
            
            if cmd == "quit" or cmd == "exit":
                break
            elif cmd == "status":
                status = bot.get_status()
                print(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")
                print(f"监听好友: {status['listen_friends']}")
                print(f"自动回复: {'开启' if status['auto_reply_enabled'] else '关闭'}")
                print(f"当前LLM: {status['current_llm']}")
                print(f"当前Agent: {status['current_agent']}")
            
            elif cmd == "add" and len(command) > 1:
                friend_name = " ".join(command[1:])
                if bot.add_friend_listener(friend_name):
                    print(f"成功添加监听好友: {friend_name}")
                else:
                    print(f"添加监听好友失败: {friend_name}")
            
            elif cmd == "remove" and len(command) > 1:
                friend_name = " ".join(command[1:])
                if bot.remove_friend_listener(friend_name):
                    print(f"成功移除监听好友: {friend_name}")
                else:
                    print(f"移除监听好友失败: {friend_name}")
            
            elif cmd == "send" and len(command) > 2:
                friend_name = command[1]
                message = " ".join(command[2:])
                if bot.send_message(friend_name, message):
                    print(f"消息已发送给 {friend_name}")
                else:
                    print(f"发送消息失败")
            
            elif cmd == "toggle":
                current = bot.config_manager.is_auto_reply_enabled()
                bot.config_manager.set_auto_reply(not current)
                print(f"自动回复已{'关闭' if current else '开启'}")
            
            elif cmd == "reload":
                if bot.reload_config():
                    print("配置重新加载成功")
                else:
                    print("配置重新加载失败")
            
            elif cmd == "help":
                print("可用命令:")
                print("  status - 查看机器人状态")
                print("  add <好友名> - 添加监听好友")
                print("  remove <好友名> - 移除监听好友")
                print("  send <好友名> <消息> - 发送消息")
                print("  toggle - 切换自动回复开关")
                print("  reload - 重新加载配置")
                print("  quit - 退出程序")
            
            else:
                print("未知命令，输入 'help' 查看可用命令")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"命令执行失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="微信智能聊天机器人")
    parser.add_argument("--config", default="config.json", help="配置文件路径")
    parser.add_argument("--setup", action="store_true", help="运行配置向导")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互模式")
    parser.add_argument("--daemon", "-d", action="store_true", help="后台运行模式")
    
    args = parser.parse_args()
    
    try:
        # 初始化配置
        config = ConfigManager(args.config)
        
        # 如果指定了setup参数或配置不完整，运行配置向导
        if args.setup or not config.get_enabled_llm_models():
            config = setup_config()
        
        # 验证配置
        errors = config.validate_config()
        if any(errors.values()):
            print("配置验证失败:")
            for category, error_list in errors.items():
                if error_list:
                    print(f"  {category}: {', '.join(error_list)}")
            print("请运行 'python main.py --setup' 重新配置")
            return 1
        
        # 创建并启动机器人
        bot = WeChatBot(config)
        
        if not bot.start():
            print("启动机器人失败")
            return 1
        
        print("机器人启动成功！")
        
        # 根据参数选择运行模式
        if args.interactive:
            interactive_mode(bot)
        elif args.daemon:
            print("后台运行模式，按 Ctrl+C 停止")
            bot.keep_running()
        else:
            print("默认运行模式，按 Ctrl+C 停止")
            print("使用 --interactive 参数启用交互模式")
            bot.keep_running()
    
    except KeyboardInterrupt:
        print("\n接收到中断信号，正在停止...")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 清理资源
        if 'bot' in locals():
            bot.stop()
        print("程序已退出")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
