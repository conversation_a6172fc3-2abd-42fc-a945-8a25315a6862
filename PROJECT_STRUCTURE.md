# 项目结构说明

## 目录结构

```
wxauto/
├── CustomFunction/           # 自定义功能模块
│   ├── LLM.py               # LLM模型管理
│   ├── Agent.py             # 智能代理
│   ├── Config.py            # 配置管理
│   └── WeChatBot.py         # 微信机器人主类
├── wxauto/                  # wxauto库源码
├── examples/                # 使用示例
│   └── basic_usage.py       # 基本使用示例
├── main.py                  # 主程序入口
├── test_bot.py              # 测试文件
├── README_Bot.md            # 机器人使用文档
├── PROJECT_STRUCTURE.md     # 项目结构说明
├── DevelopmentDocument.txt  # 开发文档
└── config.json              # 配置文件（运行时生成）
```

## 核心模块说明

### 1. LLM.py - LLM模型管理
- **BaseLLM**: LLM基类，定义统一接口
- **DeepSeekLLM**: DeepSeek模型实现
- **ChatGPTLLM**: ChatGPT模型实现  
- **QwenLLM**: Qwen模型实现
- **LLMManager**: LLM管理器，统一管理多个模型

### 2. Agent.py - 智能代理
- **Agent**: 智能代理类，包含身份、提示词等配置
- **AgentManager**: Agent管理器，管理多个Agent实例
- **create_agent_from_config**: 根据配置创建Agent的便捷函数

### 3. Config.py - 配置管理
- **ConfigManager**: 配置管理器，处理所有配置相关操作
- 支持LLM配置、Agent配置、微信配置等
- 提供配置验证、导入导出功能

### 4. WeChatBot.py - 微信机器人
- **WeChatBot**: 微信机器人主类
- 集成wxauto、LLM、Agent等组件
- 提供消息监听、自动回复、好友管理等功能

### 5. main.py - 主程序入口
- 命令行参数解析
- 配置向导
- 交互模式
- 程序启动和管理

## 功能特性

### ✅ 已实现功能

1. **多模型LLM支持**
   - DeepSeek API集成
   - ChatGPT API集成
   - Qwen API集成
   - 统一的调用接口

2. **智能代理系统**
   - 可配置的身份和提示词
   - 对话历史管理
   - 多Agent管理

3. **配置管理系统**
   - JSON配置文件
   - API密钥管理
   - 配置验证和导入导出

4. **微信集成**
   - 基于wxauto的消息监听
   - 自动回复功能
   - 好友管理
   - 自定义消息处理器

5. **用户界面**
   - 命令行交互模式
   - 配置向导
   - 状态监控

### 🔧 技术架构

- **模块化设计**: 各功能模块独立，便于维护和扩展
- **配置驱动**: 通过配置文件控制所有行为
- **异常处理**: 完善的错误处理和日志记录
- **测试覆盖**: 包含单元测试和集成测试

## 使用流程

### 1. 初始化配置
```bash
python main.py --setup
```

### 2. 启动机器人
```bash
# 默认模式
python main.py

# 交互模式
python main.py --interactive

# 后台运行
python main.py --daemon
```

### 3. 管理和监控
- 使用交互命令管理好友监听
- 实时查看机器人状态
- 动态调整配置

## 扩展指南

### 添加新的LLM模型

1. 继承`BaseLLM`类
2. 实现`chat`和`set_api_key`方法
3. 在`create_llm_manager`函数中添加支持

### 添加新的消息处理功能

1. 创建自定义处理器函数
2. 使用`bot.set_message_handler`注册
3. 或修改Agent的提示词

### 扩展配置选项

1. 在`ConfigManager._create_default_config`中添加默认值
2. 添加相应的getter/setter方法
3. 更新配置验证逻辑

## 依赖要求

- Python 3.8+
- wxauto (微信自动化)
- openai (OpenAI API)
- requests (HTTP请求)
- 其他依赖见pyproject.toml

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要提交到版本控制
2. **微信版本兼容**: 确保使用兼容的微信版本
3. **网络环境**: 需要能够访问相应的API服务
4. **使用限制**: 遵守各LLM服务的使用条款和限制

## 开发状态

✅ **已完成**:
- 核心功能实现
- 基本测试通过
- 文档编写完成

🔄 **可优化**:
- 添加更多LLM模型支持
- 增强错误处理和重试机制
- 添加更多自定义功能
- 性能优化

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交Pull Request

欢迎提交Issue和建议！
