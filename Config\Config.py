import json
import os
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器，用于管理应用程序配置"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # 创建默认配置
                self.config = self._create_default_config()
                self.save_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config = self._create_default_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "llm_models": {
                "deepseek": {
                    "api_key": "",
                    "model_name": "deepseek-chat",
                    "enabled": False
                },
                "chatgpt": {
                    "api_key": "",
                    "model_name": "gpt-3.5-turbo",
                    "enabled": False
                },
                "qwen": {
                    "api_key": "",
                    "model_name": "qwen-turbo",
                    "enabled": False
                }
            },
            "agents": {
                "default": {
                    "name": "默认助手",
                    "identity": "智能助手",
                    "llm_model": "deepseek",
                    "prompt": "",
                    "enabled": True
                }
            },
            "wechat": {
                "listen_friends": [],  # 要监听的好友列表
                "auto_reply": True,    # 是否自动回复
                "reply_delay": 1.0,    # 回复延迟（秒）
                "debug": False         # 调试模式
            },
            "general": {
                "current_llm": "deepseek",
                "current_agent": "default",
                "max_history_length": 10
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self.save_config()
    
    def get_llm_config(self, model_name: str = None) -> Dict[str, Any]:
        """获取LLM配置"""
        if model_name:
            return self.get(f"llm_models.{model_name}", {})
        else:
            return self.get("llm_models", {})
    
    def set_llm_api_key(self, model_name: str, api_key: str):
        """设置LLM API密钥"""
        self.set(f"llm_models.{model_name}.api_key", api_key)
        self.set(f"llm_models.{model_name}.enabled", bool(api_key))
    
    def get_agent_config(self, agent_name: str = None) -> Dict[str, Any]:
        """获取Agent配置"""
        if agent_name:
            return self.get(f"agents.{agent_name}", {})
        else:
            return self.get("agents", {})
    
    def add_agent_config(self, agent_name: str, config: Dict[str, Any]):
        """添加Agent配置"""
        self.set(f"agents.{agent_name}", config)
    
    def remove_agent_config(self, agent_name: str):
        """移除Agent配置"""
        agents = self.get("agents", {})
        if agent_name in agents:
            del agents[agent_name]
            self.set("agents", agents)
    
    def get_wechat_config(self) -> Dict[str, Any]:
        """获取微信配置"""
        return self.get("wechat", {})
    
    def add_listen_friend(self, friend_name: str):
        """添加监听好友"""
        friends = self.get("wechat.listen_friends", [])
        if friend_name not in friends:
            friends.append(friend_name)
            self.set("wechat.listen_friends", friends)
    
    def remove_listen_friend(self, friend_name: str):
        """移除监听好友"""
        friends = self.get("wechat.listen_friends", [])
        if friend_name in friends:
            friends.remove(friend_name)
            self.set("wechat.listen_friends", friends)
    
    def get_listen_friends(self) -> list:
        """获取监听好友列表"""
        return self.get("wechat.listen_friends", [])
    
    def is_auto_reply_enabled(self) -> bool:
        """检查是否启用自动回复"""
        return self.get("wechat.auto_reply", True)
    
    def set_auto_reply(self, enabled: bool):
        """设置自动回复开关"""
        self.set("wechat.auto_reply", enabled)
    
    def get_reply_delay(self) -> float:
        """获取回复延迟"""
        return self.get("wechat.reply_delay", 1.0)
    
    def set_reply_delay(self, delay: float):
        """设置回复延迟"""
        self.set("wechat.reply_delay", max(0.1, delay))
    
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        return self.get("wechat.debug", False)
    
    def set_debug(self, enabled: bool):
        """设置调试模式"""
        self.set("wechat.debug", enabled)
    
    def get_current_llm(self) -> str:
        """获取当前LLM模型"""
        return self.get("general.current_llm", "deepseek")
    
    def set_current_llm(self, model_name: str):
        """设置当前LLM模型"""
        self.set("general.current_llm", model_name)
    
    def get_current_agent(self) -> str:
        """获取当前Agent"""
        return self.get("general.current_agent", "default")
    
    def set_current_agent(self, agent_name: str):
        """设置当前Agent"""
        self.set("general.current_agent", agent_name)
    
    def get_enabled_llm_models(self) -> Dict[str, Dict[str, Any]]:
        """获取已启用的LLM模型配置"""
        all_models = self.get_llm_config()
        enabled_models = {}
        
        for model_name, config in all_models.items():
            if config.get("enabled", False) and config.get("api_key"):
                enabled_models[model_name] = config
        
        return enabled_models
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置的有效性"""
        errors = {
            "llm_errors": [],
            "agent_errors": [],
            "general_errors": []
        }
        
        # 验证LLM配置
        enabled_models = self.get_enabled_llm_models()
        if not enabled_models:
            errors["llm_errors"].append("没有启用的LLM模型")
        
        # 验证当前LLM是否可用
        current_llm = self.get_current_llm()
        if current_llm not in enabled_models:
            errors["llm_errors"].append(f"当前LLM模型 '{current_llm}' 未启用或配置不完整")
        
        # 验证Agent配置
        agents = self.get_agent_config()
        if not agents:
            errors["agent_errors"].append("没有配置的Agent")
        
        current_agent = self.get_current_agent()
        if current_agent not in agents:
            errors["agent_errors"].append(f"当前Agent '{current_agent}' 不存在")
        
        return errors
    
    def export_config(self, file_path: str):
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str):
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置（保留现有配置的结构）
            self._merge_config(self.config, imported_config)
            self.save_config()
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def _merge_config(self, target: dict, source: dict):
        """递归合并配置字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value


# 全局配置实例
config_manager = ConfigManager()
