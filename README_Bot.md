# 微信智能聊天机器人

基于 wxauto 和多种 LLM 模型的微信智能聊天机器人，支持自动监听好友消息并进行智能回复。

## 功能特性

- 🤖 **多模型支持**: 支持 DeepSeek、ChatGPT、Qwen 等主流 LLM 模型
- 👥 **好友监听**: 可监听指定好友的消息并自动回复
- 🎭 **多重身份**: 支持配置不同身份和提示词的 Agent
- ⚙️ **灵活配置**: 完整的配置管理系统，支持导入导出
- 🔧 **交互控制**: 提供交互式命令行界面进行实时控制
- 📝 **消息处理**: 支持自定义消息处理器

## 安装依赖

```bash
pip install openai requests wxauto
```

## 快速开始

### 1. 首次配置

运行配置向导：

```bash
python main.py --setup
```

按照提示配置：
- LLM 模型的 API Key
- Agent 身份和提示词
- 监听好友列表

### 2. 启动机器人

```bash
# 默认模式
python main.py

# 交互模式
python main.py --interactive

# 后台运行
python main.py --daemon
```

### 3. 交互式控制

在交互模式下可使用以下命令：

```
status              # 查看机器人状态
add <好友名>        # 添加监听好友
remove <好友名>     # 移除监听好友
send <好友名> <消息> # 发送消息
toggle              # 切换自动回复开关
reload              # 重新加载配置
quit                # 退出程序
```

## 配置说明

配置文件 `config.json` 包含以下部分：

### LLM 模型配置

```json
{
  "llm_models": {
    "deepseek": {
      "api_key": "your_deepseek_api_key",
      "model_name": "deepseek-chat",
      "enabled": true
    },
    "chatgpt": {
      "api_key": "your_openai_api_key", 
      "model_name": "gpt-3.5-turbo",
      "enabled": true
    },
    "qwen": {
      "api_key": "your_qwen_api_key",
      "model_name": "qwen-turbo", 
      "enabled": true
    }
  }
}
```

### Agent 配置

```json
{
  "agents": {
    "default": {
      "name": "智能助手",
      "identity": "友善的AI助手",
      "llm_model": "deepseek",
      "prompt": "你是一个友善、有帮助的AI助手...",
      "enabled": true
    },
    "professional": {
      "name": "专业顾问",
      "identity": "专业技术顾问",
      "llm_model": "chatgpt",
      "prompt": "你是一个专业的技术顾问...",
      "enabled": true
    }
  }
}
```

### 微信配置

```json
{
  "wechat": {
    "listen_friends": ["张三", "李四"],
    "auto_reply": true,
    "reply_delay": 1.0,
    "debug": false
  }
}
```

## 编程接口

### 基本使用

```python
from CustomFunction.Config import ConfigManager
from CustomFunction.WeChatBot import WeChatBot

# 初始化
config = ConfigManager()
bot = WeChatBot(config)

# 启动机器人
bot.start()

# 添加监听好友
bot.add_friend_listener("张三")

# 发送消息
bot.send_message("张三", "你好！")

# 保持运行
bot.keep_running()
```

### 自定义消息处理器

```python
def custom_handler(message_content, msg, chat):
    """自定义消息处理器"""
    if "天气" in message_content:
        return "今天天气不错！"
    return None  # 返回None使用默认Agent处理

# 设置自定义处理器
bot.set_message_handler("张三", custom_handler)
```

### LLM 管理

```python
from CustomFunction.LLM import create_llm_manager

# 创建LLM管理器
config = {
    "deepseek": {"api_key": "your_key", "model_name": "deepseek-chat"},
    "chatgpt": {"api_key": "your_key", "model_name": "gpt-3.5-turbo"}
}
llm_manager = create_llm_manager(config)

# 切换模型
llm_manager.set_current_model("chatgpt")

# 直接调用
messages = [{"role": "user", "content": "你好"}]
reply = llm_manager.chat(messages)
```

### Agent 管理

```python
from CustomFunction.Agent import Agent, AgentManager
from CustomFunction.LLM import DeepSeekLLM

# 创建Agent
llm = DeepSeekLLM("your_api_key")
agent = Agent(
    llm=llm,
    identity="技术专家", 
    prompt="你是一个技术专家..."
)

# 生成回复
reply = agent.generate_reply("Python怎么学？")

# 管理多个Agent
manager = AgentManager()
manager.add_agent("tech_expert", agent)
reply = manager.generate_reply("技术问题", "tech_expert")
```

## API Key 获取

### DeepSeek
1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账号并获取 API Key

### ChatGPT
1. 访问 [OpenAI 官网](https://platform.openai.com/)
2. 注册账号并获取 API Key

### Qwen
1. 访问 [阿里云灵积平台](https://dashscope.aliyun.com/)
2. 注册账号并获取 API Key

## 注意事项

1. **微信版本**: 确保使用兼容的微信版本
2. **API 限制**: 注意各 LLM 服务的调用频率限制
3. **网络环境**: 确保网络可以访问相应的 API 服务
4. **权限设置**: 首次运行可能需要授权微信窗口访问权限

## 故障排除

### 常见问题

1. **无法连接微信**
   - 确保微信已登录且窗口可见
   - 检查是否有其他程序占用微信

2. **API 调用失败**
   - 检查 API Key 是否正确
   - 确认网络连接正常
   - 查看是否超出调用限制

3. **消息监听失败**
   - 确认好友名称正确
   - 检查微信窗口是否正常

### 调试模式

启用调试模式获取详细日志：

```python
config.set_debug(True)
```

或在配置文件中设置：

```json
{
  "wechat": {
    "debug": true
  }
}
```

## 许可证

本项目基于 MIT 许可证开源。
