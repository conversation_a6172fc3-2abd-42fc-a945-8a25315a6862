#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信智能聊天机器人基本使用示例
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "CustomFunction"))

from CustomFunction.Config import ConfigManager
from CustomFunction.WeChatBot import WeChatBot
from CustomFunction.LLM import create_llm_manager, DeepSeekLLM
from CustomFunction.Agent import Agent, AgentManager


def example_basic_setup():
    """基本设置示例"""
    print("=== 基本设置示例 ===")
    
    # 创建配置管理器
    config = ConfigManager("examples/example_config.json")
    
    # 设置LLM API Key（请替换为你的真实API Key）
    config.set_llm_api_key("deepseek", "your_deepseek_api_key_here")
    config.set_llm_api_key("chatgpt", "your_openai_api_key_here")
    
    # 设置当前使用的模型
    config.set_current_llm("deepseek")
    
    # 添加监听好友
    config.add_listen_friend("测试好友")
    
    print("基本配置完成")
    return config


def example_custom_agent():
    """自定义Agent示例"""
    print("=== 自定义Agent示例 ===")
    
    # 创建LLM实例（需要真实的API Key）
    # llm = DeepSeekLLM("your_api_key_here")
    
    # 为了演示，这里使用模拟的LLM
    class MockLLM:
        def chat(self, messages, **kwargs):
            return "这是一个模拟回复"
        def set_api_key(self, api_key):
            pass
    
    llm = MockLLM()
    
    # 创建不同身份的Agent
    agents = {
        "助手": Agent(
            llm=llm,
            identity="友善的AI助手",
            prompt="你是一个友善、有帮助的AI助手，用简洁自然的语言回复。"
        ),
        "专家": Agent(
            llm=llm,
            identity="技术专家",
            prompt="你是一个专业的技术专家，提供准确的技术建议和解答。"
        ),
        "朋友": Agent(
            llm=llm,
            identity="好朋友",
            prompt="你是用户的好朋友，用轻松幽默的语气聊天。"
        )
    }
    
    # 测试不同Agent的回复
    test_message = "你好，最近怎么样？"
    
    for name, agent in agents.items():
        reply = agent.generate_reply(test_message)
        print(f"{name}的回复: {reply}")
    
    return agents


def example_custom_message_handler():
    """自定义消息处理器示例"""
    print("=== 自定义消息处理器示例 ===")
    
    def weather_handler(message_content, msg, chat):
        """天气查询处理器"""
        if "天气" in message_content:
            return "今天天气晴朗，温度适宜，适合外出活动！"
        return None
    
    def time_handler(message_content, msg, chat):
        """时间查询处理器"""
        if "时间" in message_content or "几点" in message_content:
            import datetime
            now = datetime.datetime.now()
            return f"现在是 {now.strftime('%Y年%m月%d日 %H:%M:%S')}"
        return None
    
    def joke_handler(message_content, msg, chat):
        """笑话处理器"""
        if "笑话" in message_content or "开心" in message_content:
            jokes = [
                "为什么程序员喜欢黑暗？因为光明会产生bug！",
                "程序员的三大美德：懒惰、急躁和傲慢。",
                "世界上最遥远的距离，是我在if里你在else里。"
            ]
            import random
            return random.choice(jokes)
        return None
    
    # 组合处理器
    def combined_handler(message_content, msg, chat):
        """组合多个处理器"""
        handlers = [weather_handler, time_handler, joke_handler]
        
        for handler in handlers:
            result = handler(message_content, msg, chat)
            if result:
                return result
        
        return None  # 没有匹配的处理器，使用默认Agent
    
    # 测试处理器
    test_messages = [
        "今天天气怎么样？",
        "现在几点了？",
        "给我讲个笑话吧",
        "你好，最近怎么样？"
    ]
    
    for message in test_messages:
        reply = combined_handler(message, None, None)
        print(f"消息: {message}")
        print(f"回复: {reply or '使用默认Agent处理'}")
        print()
    
    return combined_handler


def example_bot_usage():
    """机器人使用示例"""
    print("=== 机器人使用示例 ===")
    
    # 注意：这个示例需要真实的微信环境和API Key才能运行
    # 这里只演示代码结构
    
    try:
        # 创建配置
        config = ConfigManager("examples/example_config.json")
        
        # 检查配置是否完整
        if not config.get_enabled_llm_models():
            print("警告: 没有配置可用的LLM模型")
            print("请先配置API Key:")
            print("config.set_llm_api_key('deepseek', 'your_api_key')")
            return
        
        # 创建机器人实例
        bot = WeChatBot(config)
        
        # 设置自定义消息处理器
        custom_handler = example_custom_message_handler()
        bot.set_message_handler("测试好友", custom_handler)
        
        print("机器人配置完成，准备启动...")
        print("注意: 需要微信已登录且有相应的好友才能正常工作")
        
        # 在实际使用中，这里会启动机器人
        # bot.start()
        # bot.keep_running()
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        print("这是正常的，因为示例代码需要真实的微信环境")


def example_config_management():
    """配置管理示例"""
    print("=== 配置管理示例 ===")
    
    config = ConfigManager("examples/example_config.json")
    
    # 设置各种配置
    config.set("general.current_llm", "deepseek")
    config.set("wechat.auto_reply", True)
    config.set("wechat.reply_delay", 2.0)
    
    # 添加Agent配置
    agent_config = {
        "name": "客服助手",
        "identity": "专业客服",
        "llm_model": "chatgpt",
        "prompt": "你是一个专业的客服人员，请礼貌地回答用户的问题。",
        "enabled": True
    }
    config.add_agent_config("customer_service", agent_config)
    
    # 读取配置
    print(f"当前LLM: {config.get_current_llm()}")
    print(f"自动回复: {config.is_auto_reply_enabled()}")
    print(f"回复延迟: {config.get_reply_delay()}秒")
    print(f"监听好友: {config.get_listen_friends()}")
    
    # 验证配置
    errors = config.validate_config()
    if any(errors.values()):
        print("配置验证发现问题:")
        for category, error_list in errors.items():
            if error_list:
                print(f"  {category}: {', '.join(error_list)}")
    else:
        print("配置验证通过")
    
    # 导出配置
    config.export_config("examples/exported_config.json")
    print("配置已导出到 examples/exported_config.json")


def main():
    """主函数，运行所有示例"""
    print("微信智能聊天机器人使用示例")
    print("=" * 50)
    
    # 确保示例目录存在
    os.makedirs("examples", exist_ok=True)
    
    try:
        # 运行各个示例
        example_basic_setup()
        print()
        
        example_custom_agent()
        print()
        
        example_custom_message_handler()
        print()
        
        example_config_management()
        print()
        
        example_bot_usage()
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n示例运行完成！")
    print("要运行真实的机器人，请:")
    print("1. 配置真实的API Key")
    print("2. 确保微信已登录")
    print("3. 运行: python main.py --setup")


if __name__ == "__main__":
    main()
