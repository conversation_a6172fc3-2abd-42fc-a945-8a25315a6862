from typing import Dict, List, Optional
from .LLM import BaseLLM, LLMManager
import time


class Agent:
    """智能代理类，用于处理聊天消息并生成回复"""
    
    def __init__(
        self, 
        llm: BaseLLM, 
        identity: str = "智能助手", 
        prompt: str = None,
        name: str = "Agent"
    ):
        """
        初始化Agent
        
        Args:
            llm: LLM模型实例
            identity: 身份设定
            prompt: 系统提示词
            name: Agent名称
        """
        self.llm = llm
        self.identity = identity
        self.name = name
        self.prompt = prompt or self._default_prompt()
        self.conversation_history = []
        self.max_history_length = 10  # 最大对话历史长度
        
    def _default_prompt(self) -> str:
        """默认系统提示词"""
        return f"""你是一个{self.identity}，请根据用户的消息进行友好、有帮助的回复。
请注意：
1. 保持礼貌和友善的语气
2. 尽量提供有用的信息和建议
3. 如果不确定答案，请诚实地说明
4. 回复要简洁明了，适合微信聊天的场景
"""
    
    def set_llm(self, llm: BaseLLM):
        """设置LLM模型"""
        self.llm = llm
    
    def set_identity(self, identity: str):
        """设置身份"""
        self.identity = identity
        # 更新系统提示词
        if not hasattr(self, '_custom_prompt') or not self._custom_prompt:
            self.prompt = self._default_prompt()
    
    def set_prompt(self, prompt: str):
        """设置自定义提示词"""
        self.prompt = prompt
        self._custom_prompt = True
    
    def add_to_history(self, role: str, content: str):
        """添加对话到历史记录"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": time.time()
        })
        
        # 保持历史记录长度在限制内
        if len(self.conversation_history) > self.max_history_length * 2:
            # 保留系统消息和最近的对话
            system_msgs = [msg for msg in self.conversation_history if msg["role"] == "system"]
            recent_msgs = self.conversation_history[-(self.max_history_length * 2):]
            self.conversation_history = system_msgs + recent_msgs
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_messages_for_llm(self, user_message: str) -> List[Dict[str, str]]:
        """构建发送给LLM的消息列表"""
        messages = []
        
        # 添加系统提示词
        messages.append({
            "role": "system",
            "content": self.prompt
        })
        
        # 添加历史对话（只保留role和content）
        for msg in self.conversation_history[-self.max_history_length:]:
            if msg["role"] in ["user", "assistant"]:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        # 添加当前用户消息
        messages.append({
            "role": "user",
            "content": user_message
        })
        
        return messages
    
    def generate_reply(self, user_message: str, **kwargs) -> str:
        """生成回复"""
        try:
            # 构建消息列表
            messages = self.get_messages_for_llm(user_message)
            
            # 调用LLM生成回复
            reply = self.llm.chat(messages, **kwargs)
            
            # 添加到历史记录
            self.add_to_history("user", user_message)
            self.add_to_history("assistant", reply)
            
            return reply
            
        except Exception as e:
            error_msg = f"生成回复时发生错误: {str(e)}"
            print(error_msg)
            return "抱歉，我现在无法回复您的消息，请稍后再试。"
    
    def get_info(self) -> Dict:
        """获取Agent信息"""
        return {
            "name": self.name,
            "identity": self.identity,
            "llm_type": type(self.llm).__name__,
            "history_length": len(self.conversation_history),
            "max_history_length": self.max_history_length
        }
    
    def set_max_history_length(self, length: int):
        """设置最大历史记录长度"""
        self.max_history_length = max(1, length)


class AgentManager:
    """Agent管理器，用于管理多个Agent实例"""
    
    def __init__(self):
        self.agents = {}
        self.default_agent = None
    
    def add_agent(self, name: str, agent: Agent):
        """添加Agent"""
        self.agents[name] = agent
        if self.default_agent is None:
            self.default_agent = name
    
    def remove_agent(self, name: str):
        """移除Agent"""
        if name in self.agents:
            del self.agents[name]
            if self.default_agent == name:
                self.default_agent = list(self.agents.keys())[0] if self.agents else None
    
    def get_agent(self, name: str = None) -> Optional[Agent]:
        """获取Agent实例"""
        target_name = name or self.default_agent
        return self.agents.get(target_name)
    
    def set_default_agent(self, name: str):
        """设置默认Agent"""
        if name in self.agents:
            self.default_agent = name
            return True
        return False
    
    def list_agents(self) -> List[str]:
        """列出所有Agent"""
        return list(self.agents.keys())
    
    def generate_reply(self, user_message: str, agent_name: str = None, **kwargs) -> str:
        """使用指定Agent生成回复"""
        agent = self.get_agent(agent_name)
        if agent:
            return agent.generate_reply(user_message, **kwargs)
        return "未找到可用的Agent"


def create_agent_from_config(config: Dict, llm_manager: LLMManager) -> Agent:
    """根据配置创建Agent
    
    Args:
        config: Agent配置字典
        llm_manager: LLM管理器实例
    
    Returns:
        Agent实例
    """
    llm_name = config.get("llm_model", "deepseek")
    llm = llm_manager.get_current_model()
    
    # 如果指定了特定的LLM模型，尝试获取
    if llm_name in llm_manager.list_models():
        target_llm = llm_manager.models.get(llm_name)
        if target_llm:
            llm = target_llm
    
    if not llm:
        raise ValueError("未找到可用的LLM模型")
    
    return Agent(
        llm=llm,
        identity=config.get("identity", "智能助手"),
        prompt=config.get("prompt"),
        name=config.get("name", "Agent")
    )
