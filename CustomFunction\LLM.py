import openai
import requests
from typing import Dict, List, Optional
from abc import ABC, abstractmethod


class BaseLLM(ABC):
    """LLM基类，定义统一的接口"""

    def __init__(self, api_key: str, model_name: str = None):
        self.api_key = api_key
        self.model_name = model_name

    @abstractmethod
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """发送聊天消息并获取回复"""
        pass

    @abstractmethod
    def set_api_key(self, api_key: str):
        """设置API密钥"""
        pass


class DeepSeekLLM(BaseLLM):
    """DeepSeek模型实现"""

    def __init__(self, api_key: str, model_name: str = "deepseek-chat"):
        super().__init__(api_key, model_name)
        self.base_url = "https://api.deepseek.com/v1"
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

    def set_api_key(self, api_key: str):
        """设置API密钥"""
        self.api_key = api_key
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """发送聊天消息并获取回复"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 1000)
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"DeepSeek API调用失败: {str(e)}"


class ChatGPTLLM(BaseLLM):
    """ChatGPT模型实现"""

    def __init__(self, api_key: str, model_name: str = "gpt-3.5-turbo"):
        super().__init__(api_key, model_name)
        self.client = openai.OpenAI(api_key=self.api_key)

    def set_api_key(self, api_key: str):
        """设置API密钥"""
        self.api_key = api_key
        self.client = openai.OpenAI(api_key=self.api_key)

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """发送聊天消息并获取回复"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=kwargs.get('temperature', 0.7),
                max_tokens=kwargs.get('max_tokens', 1000)
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"ChatGPT API调用失败: {str(e)}"


class QwenLLM(BaseLLM):
    """Qwen模型实现"""

    def __init__(self, api_key: str, model_name: str = "qwen-turbo"):
        super().__init__(api_key, model_name)
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

    def set_api_key(self, api_key: str):
        """设置API密钥"""
        self.api_key = api_key

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """发送聊天消息并获取回复"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

            data = {
                "model": self.model_name,
                "input": {
                    "messages": messages
                },
                "parameters": {
                    "temperature": kwargs.get('temperature', 0.7),
                    "max_tokens": kwargs.get('max_tokens', 1000)
                }
            }

            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            if 'output' in result and 'text' in result['output']:
                return result['output']['text']
            else:
                return f"Qwen API返回格式异常: {result}"

        except Exception as e:
            return f"Qwen API调用失败: {str(e)}"


class LLMManager:
    """LLM管理器，统一管理多个LLM模型"""

    def __init__(self):
        self.models = {}
        self.current_model = None

    def add_model(self, name: str, llm_instance: BaseLLM):
        """添加LLM模型"""
        self.models[name] = llm_instance
        if self.current_model is None:
            self.current_model = name

    def set_current_model(self, name: str):
        """设置当前使用的模型"""
        if name in self.models:
            self.current_model = name
            return True
        return False

    def get_current_model(self) -> Optional[BaseLLM]:
        """获取当前模型实例"""
        if self.current_model and self.current_model in self.models:
            return self.models[self.current_model]
        return None

    def chat(self, messages: List[Dict[str, str]], model_name: str = None, **kwargs) -> str:
        """使用指定模型或当前模型进行聊天"""
        target_model = model_name or self.current_model
        if target_model and target_model in self.models:
            return self.models[target_model].chat(messages, **kwargs)
        return "未找到可用的LLM模型"

    def list_models(self) -> List[str]:
        """列出所有可用的模型"""
        return list(self.models.keys())

    def remove_model(self, name: str):
        """移除模型"""
        if name in self.models:
            del self.models[name]
            if self.current_model == name:
                self.current_model = list(self.models.keys())[0] if self.models else None


# 便捷函数
def create_llm_manager(config: Dict[str, Dict[str, str]]) -> LLMManager:
    """根据配置创建LLM管理器

    Args:
        config: 配置字典，格式如下:
        {
            "deepseek": {"api_key": "your_key", "model_name": "deepseek-chat"},
            "chatgpt": {"api_key": "your_key", "model_name": "gpt-3.5-turbo"},
            "qwen": {"api_key": "your_key", "model_name": "qwen-turbo"}
        }
    """
    manager = LLMManager()

    for model_type, model_config in config.items():
        api_key = model_config.get("api_key")
        model_name = model_config.get("model_name")

        if not api_key:
            continue

        if model_type.lower() == "deepseek":
            llm = DeepSeekLLM(api_key, model_name or "deepseek-chat")
        elif model_type.lower() == "chatgpt":
            llm = ChatGPTLLM(api_key, model_name or "gpt-3.5-turbo")
        elif model_type.lower() == "qwen":
            llm = QwenLLM(api_key, model_name or "qwen-turbo")
        else:
            continue

        manager.add_model(model_type, llm)

    return manager